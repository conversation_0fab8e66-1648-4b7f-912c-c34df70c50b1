package com.ously.gamble.payment.coinspaid.api.restapi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public record CPPGetSupportedCurrenciesRequest(boolean visible) {

    @Override
    public String toString() {
        return "CPPGetSupportedCurrenciesRequest{" +
               "visible=" + visible +
               '}';
    }
}
