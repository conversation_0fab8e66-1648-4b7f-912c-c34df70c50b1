package com.ously.gamble.payment.adyen.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "payment.adyen")
public class AdyenConfig {

    String username = "<EMAIL>";
    String password = "~f36Xx~)(>NK4K[3%wR{yz;r]";
    String apikey = "AQEphmfuXNWTK0Qc+iSfh3c0vsOaRY5eKp5IcCDw5uraUHgEcX1skv2y8D0QwV1bDb7kfNy1WIxIIkxgBw==-6SOT1KGPRWWJXcf8la7y/JYyrhilnx2fYMkrY3i9oAU=-(mj?GYb3e&2frJc2";
    String checkouturl = "https://checkout-test.adyen.com/checkout/v69";
    String clientkey = "test_PZ6GCEP44RDMTBYX2OWQIMUDYYWB3I6B";
    String merchantaccount = "OuslyGamesGmbHECOM";
    String hmacnotification = "0AC28921F7B139E24EBAA1F14DA3A74C65056072BAE47C082B7F533BE0DD79C5";

    public String getHmacnotification() {
        return hmacnotification;
    }

    public void setHmacnotification(String hmacnotification) {
        this.hmacnotification = hmacnotification;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getCheckouturl() {
        return checkouturl;
    }

    public void setCheckouturl(String checkouturl) {
        this.checkouturl = checkouturl;
    }

    public String getClientkey() {
        return clientkey;
    }

    public void setClientkey(String clientkey) {
        this.clientkey = clientkey;
    }

    public String getMerchantaccount() {
        return merchantaccount;
    }

    public void setMerchantaccount(String merchantaccount) {
        this.merchantaccount = merchantaccount;
    }


}
