package com.ously.gamble.payment.mobile.config;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisher.Builder;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.ServiceAccountCredentials;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;

@Configuration()
public class PlaystoreVerificationConfig {


    @Bean
    AndroidPublisher playstorePublisher(PlaystoreConfig config) throws GeneralSecurityException, IOException {

        var scopes = Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER);

        // Credentials handler
        HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        var gsonFactory = GsonFactory.getDefaultInstance();
        var serviceAccountCredentials = ServiceAccountCredentials.fromStream(PlaystoreVerificationConfig.class.getResourceAsStream("/secrets/spinarena_googleplay.json"));
        final var googleCredentials = serviceAccountCredentials
                .createScoped(scopes);
        HttpRequestInitializer requestInitializer = new HttpCredentialsAdapter(googleCredentials);
        // Now setup data to verify
        return new Builder(httpTransport, gsonFactory, requestInitializer)
                .setApplicationName(serviceAccountCredentials.getProjectId())
                .build();
    }

}
