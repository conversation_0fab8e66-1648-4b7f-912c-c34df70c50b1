CREATE TABLE if not exists `safe_active`
(
    `user_id`         bigint         NOT NULL,
    `safe_id`     VARCHAR(40)         NOT NULL,
    `safe_type`     VARCHAR(20)         NOT NULL,
    `opens_at`      TIMESTAMP NOT NULL,
    `rewards` VARCHAR(80) NOT NULL,
    PRIMARY KEY (`user_id`, `safe_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


CREATE TABLE if not exists `safe_backlog`
(
    `user_id`         bigint         NOT NULL,
    `safe_id`     VARCHAR(40)         NOT NULL,
    `safe_type`     VARCHAR(20)         NOT NULL,
    `created_at`      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`, `safe_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
