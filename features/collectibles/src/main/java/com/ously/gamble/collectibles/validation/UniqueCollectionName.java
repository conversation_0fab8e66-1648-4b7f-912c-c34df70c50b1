package com.ously.gamble.collectibles.validation;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UniqueCollectionNameValidator.class)
public @interface UniqueCollectionName {
    String message() default "Collection with this name already exists";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

@Component
class UniqueCollectionNameValidator implements ConstraintValidator<UniqueCollectionName, String> {

    @Autowired
    private CardCollectionRepository cardCollectionRepository;

    @Override
    public void initialize(UniqueCollectionName constraintAnnotation) {
        // Инициализация не нужна для простого валидатора поля
    }

    @Override
    public boolean isValid(String name, ConstraintValidatorContext context) {
        if (name == null || name.trim().isEmpty()) {
            return true; // другая аннотация проверит @NotBlank и т.п.
        }

        // Простая проверка уникальности имени без учета excludeId
        // Для более сложной логики с excludeId используйте валидацию на уровне класса
        List<CardCollection> existing = cardCollectionRepository.findByNameIgnoreCase(name.trim());
        return existing.isEmpty();
    }
}
