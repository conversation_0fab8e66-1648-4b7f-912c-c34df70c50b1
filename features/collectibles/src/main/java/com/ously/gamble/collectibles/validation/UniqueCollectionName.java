package com.ously.gamble.collectibles.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import org.apache.logging.log4j.core.config.plugins.validation.ConstraintValidator;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UniqueCollectionNameValidator.class)
public @interface UniqueCollectionName {
    String message() default "Collection with this name already exists";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    String excludeIdField() default "";
}

class UniqueCollectionNameValidator implements ConstraintValidator<UniqueCollectionName, String> {

    private String excludeIdField;

    @Autowired
    private CardCollectionRepository cardCollectionRepository;

    @Override
    public void initialize(UniqueCollectionName constraintAnnotation) {
        this.excludeIdField = constraintAnnotation.excludeIdField();
    }

    @Override
    public boolean isValid(String name, ConstraintValidatorContext context) {
        if (name == null || name.trim().isEmpty()) {
            return true; // другая аннотация проверит @NotBlank и т.п.
        }

        // Получаем текущий объект, к которому применяется аннотация
        Object rootBean = context.unwrap(HibernateConstraintValidatorContext.class).getConstraintValidatorPayload();
        Integer excludeId = null;

        if (rootBean != null && !excludeIdField.isEmpty()) {
            try {
                var propertyDescriptor = BeanUtils.getPropertyDescriptor(rootBean.getClass(), excludeIdField);
                if (propertyDescriptor != null) {
                    Object value = propertyDescriptor.getReadMethod().invoke(rootBean);
                    if (value instanceof Integer) {
                        excludeId = (Integer) value;
                    }
                }
            } catch (Exception e) {
                // ignore, fallback to null
            }
        }

        return cardCollectionRepository.findByNameIgnoreCase(name.trim()).stream()
                .noneMatch(c -> excludeId == null || !c.getId().equals(excludeId));
    }
}

