//package com.ously.gamble.collectibles.persistence.repository;
//
//import com.ously.gamble.collectibles.persistence.model.Card;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//import org.springframework.stereotype.Repository;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//@Repository
//public interface CardRepository extends JpaRepository<Card, Integer> {
//
//    List<Card> findByCardCollectionId(Integer cardCollectionId);
//
//    Page<Card> findByCardCollectionId(Integer cardCollectionId, Pageable pageable);
//
//    List<Card> findByStatus(Card.CardStatus status);
//
//    Page<Card> findByStatus(Card.CardStatus status, Pageable pageable);
//
//    List<Card> findByRarityLevel(Byte rarityLevel);
//
//    Page<Card> findByRarityLevel(Byte rarityLevel, Pageable pageable);
//
//    List<Card> findByCardCollectionIdAndStatus(Integer cardCollectionId, Card.CardStatus status);
//
//    List<Card> findByCardCollectionIdAndRarityLevel(Integer cardCollectionId, Byte rarityLevel);
//
//    @Query("SELECT c FROM Card c WHERE c.status = 'ENABLED' " +
//           "AND c.startDate <= :now " +
//           "AND (c.endDate IS NULL OR c.endDate > :now) " +
//           "AND c.cardCollection.status = 'ENABLED' " +
//           "AND c.cardCollection.startDate <= :now " +
//           "AND (c.cardCollection.endDate IS NULL OR c.cardCollection.endDate > :now)")
//    List<Card> findActiveCards(@Param("now") LocalDateTime now);
//
//    @Query("SELECT c FROM Card c WHERE c.status = 'ENABLED' " +
//           "AND c.startDate <= :now " +
//           "AND (c.endDate IS NULL OR c.endDate > :now) " +
//           "AND c.cardCollection.status = 'ENABLED' " +
//           "AND c.cardCollection.startDate <= :now " +
//           "AND (c.cardCollection.endDate IS NULL OR c.cardCollection.endDate > :now)")
//    Page<Card> findActiveCards(@Param("now") LocalDateTime now, Pageable pageable);
//
//    @Query("SELECT c FROM Card c WHERE c.cardCollection.id = :collectionId " +
//           "AND c.status = 'ENABLED' " +
//           "AND c.startDate <= :now " +
//           "AND (c.endDate IS NULL OR c.endDate > :now)")
//    List<Card> findActiveCardsByCollection(@Param("collectionId") Integer collectionId,
//                                         @Param("now") LocalDateTime now);
//
//    @Query("SELECT c FROM Card c WHERE c.cardCollection.id = :collectionId " +
//           "AND c.status = 'ENABLED' " +
//           "AND c.startDate <= :now " +
//           "AND (c.endDate IS NULL OR c.endDate > :now)")
//    Page<Card> findActiveCardsByCollection(@Param("collectionId") Integer collectionId,
//                                         @Param("now") LocalDateTime now, Pageable pageable);
//
//    @Query("SELECT c FROM Card c WHERE c.status = 'EXPIRED' " +
//           "OR (c.endDate IS NOT NULL AND c.endDate <= :now)")
//    List<Card> findExpiredCards(@Param("now") LocalDateTime now);
//
//    List<Card> findByNameContainingIgnoreCase(String name);
//
//    List<Card> findByCardCollectionIdOrderBySortOrderAscCreatedAtAsc(Integer cardCollectionId);
//
//    @Query("SELECT c FROM Card c WHERE c.rarityLevel >= :minRarity")
//    List<Card> findByRarityLevelGreaterThanEqual(@Param("minRarity") Byte minRarity);
//
//    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 3")
//    List<Card> findRareCards();
//
//    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 1")
//    List<Card> findCommonCards();
//
//    @Query("SELECT c FROM Card c WHERE c.rarityLevel = 2")
//    List<Card> findUncommonCards();
//
//    @Query("SELECT COUNT(c) FROM Card c WHERE c.cardCollection.id = :collectionId")
//    long countByCollectionId(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT COUNT(c) FROM Card c WHERE c.cardCollection.id = :collectionId AND c.status = :status")
//    long countByCollectionIdAndStatus(@Param("collectionId") Integer collectionId,
//                                    @Param("status") Card.CardStatus status);
//
//    @Query("SELECT COUNT(c) FROM Card c WHERE c.rarityLevel = :rarity")
//    long countByRarityLevel(@Param("rarity") Byte rarity);
//
//    @Query("SELECT c FROM Card c LEFT JOIN FETCH c.rewards WHERE c.id = :id")
//    Card findByIdWithRewards(@Param("id") Integer id);
//
//    @Query("SELECT c FROM Card c " +
//           "LEFT JOIN FETCH c.cardCollection cc " +
//           "LEFT JOIN FETCH c.rewards r " +
//           "WHERE c.id = :id")
//    Card findByIdWithCollectionAndRewards(@Param("id") Integer id);
//
//    List<Card> findByStartDateBetween(LocalDateTime startDate, LocalDateTime endDate);
//
//    List<Card> findByEndDateBetween(LocalDateTime startDate, LocalDateTime endDate);
//
//    @Query("SELECT c FROM Card c WHERE c.endDate IS NOT NULL " +
//           "AND c.endDate BETWEEN :startDate AND :endDate")
//    List<Card> findExpiringBetween(@Param("startDate") LocalDateTime startDate,
//                                 @Param("endDate") LocalDateTime endDate);
//}
