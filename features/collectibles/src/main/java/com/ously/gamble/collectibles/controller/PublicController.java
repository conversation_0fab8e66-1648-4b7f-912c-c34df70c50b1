package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.service.CollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/collections")
@Tag(name = "Public Collections", description = "Public access to active collections")
public class PublicController {

    private final CollectionService collectionService;
    private final CardCollectionMapper mapper;

    public PublicController(CollectionService collectionService, CardCollectionMapper mapper) {
        this.collectionService = collectionService;
        this.mapper = mapper;
    }

    @Operation(description = "Get active collections")
    @GetMapping
    public Page<CardCollectionDto.CardCollectionSummaryResponse> getActiveCollections(
            @PageableDefault(size = 20) Pageable pageable) {
        return collectionService.findActiveCollections(pageable)
                .map(mapper::toSummaryResponse);
    }

    @Operation(description = "Get active collection details")
    @GetMapping("/{id}")
    public CardCollectionDto.CardCollectionResponse getActiveCollection(@PathVariable Integer id) {
        return collectionService.findActiveCollectionById(id)
                .map(mapper::toResponse)
                .orElse(null);
    }
}
