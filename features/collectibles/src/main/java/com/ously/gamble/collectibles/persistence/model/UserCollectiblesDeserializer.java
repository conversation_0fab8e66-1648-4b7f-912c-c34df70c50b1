package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.collections.api.map.primitive.MutableIntIntMap;
import org.eclipse.collections.impl.map.mutable.primitive.IntIntHashMap;

import java.io.IOException;

public class UserCollectiblesDeserializer extends JsonDeserializer<UserCollectibles> {
    @Override
    public UserCollectibles deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        UserCollectibles uc = new UserCollectibles();
        if (!node.isEmpty()) {
            MutableIntIntMap iiMap = new IntIntHashMap(node.size());
            node.fields().forEachRemaining((a) -> {
                int k = Integer.parseInt(a.getKey());
                int v = a.getValue().asInt();
                iiMap.put(k, v);
            });
            uc.setCollectibleCounts(iiMap);
        }

        return uc;
    }
}
