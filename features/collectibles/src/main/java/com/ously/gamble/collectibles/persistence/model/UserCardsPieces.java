//package com.ously.gamble.collectibles.persistence.model;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import jakarta.persistence.*;
//import org.hibernate.annotations.JdbcTypeCode;
//import org.hibernate.type.SqlTypes;
//
//import java.util.Objects;
//
//@Entity
//@Table(name = "user_cards_pieces")
//public class UserCardsPieces {
//
//    @Id
//    @Column(name = "user_id")
//    private Long userId;
//
//    @JdbcTypeCode(SqlTypes.JSON)
//    @Column(name = "pieces_data", nullable = false, columnDefinition = "JSON")
//    private JsonNode piecesData;
//
//    // Constructors
//    protected UserCardsPieces() {}
//
//    protected UserCardsPieces(Long userId) {
//        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
//        initializeEmptyData();
//    }
//
//    protected UserCardsPieces(Long userId, JsonNode piecesData) {
//        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
//        this.piecesData = Objects.requireNonNull(piecesData, "Pieces data cannot be null");
//    }
//
//    // Factory methods
//    public static UserCardsPieces createForUser(Long userId) {
//        return new UserCardsPieces(userId);
//    }
//
//    public static UserCardsPieces createWithData(Long userId, JsonNode piecesData) {
//        return new UserCardsPieces(userId, piecesData);
//    }
//
//    // Rich Domain Model methods
//    public boolean hasData() {
//        return piecesData != null && !piecesData.isNull() && !piecesData.isEmpty();
//    }
//
//    public void initializeEmptyData() {
//        // Инициализация пустой структуры JSON для нового пользователя
//        // Структура: {"collections": {"collectionId": {"cards": {"cardId": {"pieces": count}}}}}
//        this.piecesData = createEmptyPiecesStructure();
//    }
//
//    private JsonNode createEmptyPiecesStructure() {
//        // Создание пустой JSON структуры
//        // В реальной реализации здесь будет создание ObjectNode
//        return null; // Placeholder - нужно будет реализовать с ObjectMapper
//    }
//
//    // Modifiers
//    public void updatePiecesData(JsonNode newData) {
//        this.piecesData = Objects.requireNonNull(newData, "Pieces data cannot be null");
//    }
//
//    public void resetData() {
//        initializeEmptyData();
//    }
//
//    public boolean hasCollection(Integer collectionId) {
//        if (!hasData()) return false;
//        JsonNode collections = piecesData.get("collections");
//        return collections != null && collections.has(collectionId.toString());
//    }
//
//    public boolean hasCard(Integer collectionId, Integer cardId) {
//        if (!hasCollection(collectionId)) return false;
//        JsonNode collection = piecesData.get("collections").get(collectionId.toString());
//        JsonNode cards = collection.get("cards");
//        return cards != null && cards.has(cardId.toString());
//    }
//
//    public int getPiecesCount(Integer collectionId, Integer cardId) {
//        if (!hasCard(collectionId, cardId)) return 0;
//        JsonNode card = piecesData.get("collections")
//                .get(collectionId.toString())
//                .get("cards")
//                .get(cardId.toString());
//        JsonNode pieces = card.get("pieces");
//        return pieces != null ? pieces.asInt(0) : 0;
//    }
//
//    public boolean isCardCompleted(Integer collectionId, Integer cardId, int requiredPieces) {
//        return getPiecesCount(collectionId, cardId) >= requiredPieces;
//    }
//
//    public boolean isCollectionCompleted(Integer collectionId, java.util.List<Card> cards, int requiredPiecesPerCard) {
//        if (!hasCollection(collectionId)) return false;
//        return cards.stream()
//                .allMatch(card -> isCardCompleted(collectionId, card.getId(), requiredPiecesPerCard));
//    }
//
//    public int getCollectionProgress(Integer collectionId, java.util.List<Card> cards, int requiredPiecesPerCard) {
//        if (!hasCollection(collectionId) || cards.isEmpty()) return 0;
//
//        long completedCards = cards.stream()
//                .mapToLong(card -> isCardCompleted(collectionId, card.getId(), requiredPiecesPerCard) ? 1 : 0)
//                .sum();
//
//        return (int) ((completedCards * 100) / cards.size());
//    }
//
//    // Getters
//    public Long getUserId() {
//        return userId;
//    }
//
//    public JsonNode getPiecesData() {
//        return piecesData;
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) return true;
//        if (o == null || getClass() != o.getClass()) return false;
//        UserCardsPieces that = (UserCardsPieces) o;
//        return Objects.equals(userId, that.userId);
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hash(userId);
//    }
//
//    @Override
//    public String toString() {
//        return "UserCardsPieces{" +
//                "userId=" + userId +
//                ", hasData=" + hasData() +
//                ", updatedAt=" + updatedAt +
//                '}';
//    }
//}
