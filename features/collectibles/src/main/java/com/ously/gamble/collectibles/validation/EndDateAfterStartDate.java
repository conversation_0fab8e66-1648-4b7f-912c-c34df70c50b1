package com.ously.gamble.collectibles.validation;


import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.beans.BeanUtils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.LocalDateTime;

@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EndDateAfterStartDateValidator.class)
public @interface EndDateAfterStartDate {
    String message() default "End date must be after start date";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

class EndDateAfterStartDateValidator implements ConstraintValidator<EndDateAfterStartDate, Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        if (o == null) {
            return true;
        }

        try {
            LocalDateTime start = (LocalDateTime) BeanUtils.getPropertyDescriptor(o.getClass(), "startDate")
                    .getReadMethod().invoke(o);

            LocalDateTime end = (LocalDateTime) BeanUtils.getPropertyDescriptor(o.getClass(), "endDate")
                    .getReadMethod().invoke(o);

            if (start == null || end == null) return true;

            return end.isAfter(start);
        } catch (Exception e) {
            return false;
        }
    }
}
