package com.ously.gamble.milestones.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;

import java.time.Instant;

public record MilestoneDto(long milestoneId,
                           @JsonSerialize(using = InstantSerializer.class)
                           Instant validFrom,
                           @JsonSerialize(using = InstantSerializer.class)
                           Instant validTo,
                           MilestoneType type,
                           int stages,
                           float[] scores,
                           int[] rewards,
                           String[] tokens,
                           int vendorId,
                           int gameId,
                           float minBet,
                           @JsonProperty(defaultValue = "-1")
                           float maxBet,
                           float minWin,
                           int minBetlevel,
                           @JsonProperty(defaultValue = "-1")
                           int maxBetlevel) {

}
