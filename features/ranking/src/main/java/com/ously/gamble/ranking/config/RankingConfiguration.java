package com.ously.gamble.ranking.config;

import com.ously.gamble.api.rankings.RankingRewardDefinitionItem;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "monitor.rankings")
public class RankingConfiguration {

    List<RankingRewardDefinitionItem> rewards = new ArrayList<>();

    /**
     * enable the tournament/ranking monitor
     */
    boolean enabled;

    String txQueueName = "mon_ranking_updates";

    boolean tokensEnabled = false;

    double jackpotFactor = 0.025;
    int jackpotUsercount = 10;

    double percWinMax = 0.04;
    double percWagerSum = 0.01;
    double percWinSum = 0.02;
    double percDefault = 0.01;

    public double getPercWinMax() {
        return percWinMax;
    }

    public void setPercWinMax(double percWinMax) {
        this.percWinMax = percWinMax;
    }

    public double getPercWagerSum() {
        return percWagerSum;
    }

    public void setPercWagerSum(double percWagerSum) {
        this.percWagerSum = percWagerSum;
    }

    public double getPercWinSum() {
        return percWinSum;
    }

    public void setPercWinSum(double percWinSum) {
        this.percWinSum = percWinSum;
    }

    public double getPercDefault() {
        return percDefault;
    }

    public void setPercDefault(double percDefault) {
        this.percDefault = percDefault;
    }

    public boolean isTokensEnabled() {
        return tokensEnabled;
    }

    public void setTokensEnabled(boolean tokensEnabled) {
        this.tokensEnabled = tokensEnabled;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getTxQueueName() {
        return txQueueName;
    }

    public void setTxQueueName(String txQueueName) {
        this.txQueueName = txQueueName;
    }

    public List<RankingRewardDefinitionItem> getRewards() {
        return rewards;
    }

    public void setRewards(List<RankingRewardDefinitionItem> rewards) {
        this.rewards = rewards;
    }

    public double getJackpotFactor() {
        return jackpotFactor;
    }

    public void setJackpotFactor(double jackpotFactor) {
        this.jackpotFactor = jackpotFactor;
    }

    public int getJackpotUsercount() {
        return jackpotUsercount;
    }

    public void setJackpotUsercount(int jackpotUsercount) {
        this.jackpotUsercount = jackpotUsercount;
    }
}
