package com.ously.gamble.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.user.LoginRequest;
import com.ously.gamble.api.user.SignUpRequest;
import com.ously.gamble.api.user.UserIdentityAvailability;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.repository.WalletRepository;
import org.junit.BeforeClass;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.RabbitMQContainer;

import java.io.IOException;
import java.util.Locale;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SuppressWarnings("rawtypes")
@ActiveProfiles("integrationtest")
@TestPropertySource(properties = "spring.main.allow-bean-definition-overriding=true")
public class BaseTestContext {
    private static final Logger log = LoggerFactory.getLogger(BaseTestContext.class);

    private static BaeldungMysqlContainer mysqlInstance;
    private static RabbitMQContainer rabbitInstance;
    private static GenericContainer redisInstance;
//    private static PulsarContainer pulsarInstance;


    @BeforeClass
    public static void setDefaultLocale() {
        Locale.setDefault(Locale.US);
    }

    static {
        log.info("Booting Testcontainers");
        try {
            var stMs = System.currentTimeMillis();
            mysqlInstance = BaeldungMysqlContainer.getInstance();
            rabbitInstance = OuslyRabbitMQContainer.getInstance();
            redisInstance = RedisContainer.getInstance();
//            pulsarInstance = OuslyPulsarContainer.getInstance();


            Stream.of(mysqlInstance, rabbitInstance, redisInstance).parallel().forEach(GenericContainer::start);

            log.info("Booting Testcontainers finished in {} seconds",
                    (System.currentTimeMillis() - stMs) / 1000L);
        } catch (Exception e) {
            log.error("Booting Testcontainers failed", e);
            throw new RuntimeException("Testcontainer startup failed");
        }
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("ously.redisson.host", redisInstance::getHost);
        registry.add("ously.redisson.port", redisInstance::getFirstMappedPort);

        registry.add("ously.redisson.password", () -> "fatass69");
        registry.add("spring.rabbitmq.host", rabbitInstance::getHost);

        registry.add("spring.rabbitmq.port", rabbitInstance::getAmqpPort);
        registry.add("spring.rabbitmq.addresses",
                rabbitInstance::getAmqpUrl);
//        registry.add("spring.pulsar.client.service-url", pulsarInstance::getPulsarBrokerUrl);
//        registry.add("spring.pulsar.admin.service-url", pulsarInstance::getHttpServiceUrl);

    }

    @Autowired
    RedissonClient client;

    public RedissonClient getRedissonClient() {
        return client;
    }

    @LocalServerPort
    protected int port;
    @Autowired
    protected TestRestTemplate restTemplate;

    @Autowired
    ObjectMapper om;

    @Autowired(required = false)
    UserManagementService uMgmt;

    @Autowired
    private WalletRepository wRepo;

    public Wallet getWallet(long uid) {
        return wRepo.getWalletById(uid);
    }

    /**
     * creates a "testuser" with "testpassword" password and impregnates restTemplate with bearer token
     */
    public void signupAndSignin(String username) throws IOException {
        var alreadyCreated = checkUser(username);
        if (!alreadyCreated) {
            createUser(username, "testpassword");
            signinUser(username, "testpassword");
        }

    }

    private boolean checkUser(String username) {
        var result = restTemplate.getForEntity("http://localhost:" + port + "/api/user/checkUsernameAvailability?username=" + username, UserIdentityAvailability.class, "username", "testuser");
        assertEquals(200, result.getStatusCode().value());
        return !result.getBody().getAvailable();
    }

    private void signinUser(String username, String password) throws IOException {
        // Sign in, retrieve token
        var lir = new LoginRequest();
        lir.setPassword(password);
        lir.setUsernameOrEmail(username);
        var result = this.restTemplate.postForEntity("http://localhost:" + port + "/api/auth/signin", lir, String.class);
        assertEquals(200, result.getStatusCode().value());

        var tokenResult = result.getBody();
        // the result is a json so we retrieve the raw token

        var jsonNode = om.readTree(tokenResult);
        var token = jsonNode.get("accessToken").textValue();
        addToken(token);
        assertNotNull(token);
    }

    private void createUser(String displayName, String password) {
        var sur = new SignUpRequest();
        sur.setEmail(displayName + "<EMAIL>");
        sur.setDisplayName(displayName);
        sur.setPassword(password);
        var result = this.restTemplate.postForEntity("http://localhost:" + port + "/api/auth/signup", sur, String.class);
        assertEquals(201, result.getStatusCode().value());

        // Admin automatism
        if ("admin".equals(displayName)) {
            var byUsername = uMgmt.findByEmail(sur.getEmail());
            var aUser = byUsername.get();
            aUser.setSystemUser(Boolean.TRUE);
            uMgmt.saveUser(aUser);
        }
    }

    public void addToken(final String token) {
        this.restTemplate.getRestTemplate().getInterceptors().add((request, body, execution) -> {
                    request.getHeaders().add("Authorization", "Bearer " + token);
                    return execution.execute(request, body);
                }

        );
    }

}
