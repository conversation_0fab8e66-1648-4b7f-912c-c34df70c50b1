#
# PROFILE
#
spring.jmx.enabled=false
spring.profiles.active=dev
spring.application.name=ously-backend
spring.config.import=classpath:common-config/shared.properties
server.port=8080
#
# Jackson View
#
spring.jackson.mapper.DEFAULT_VIEW_INCLUSION=true
#
# Swagger
#
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
springdoc.swagger-ui.urlsPrimaryName=APP
springdoc.paths-to-match=/api/app/**,/api/l/**

#
# Feature and base properties
#
features.autoActive=false
features.mailfrom=<EMAIL>
features.baseUrl=http://localhost:8080
features.fakeValidation=true
features.stage=dev
features.testing=true
#
# Payment exclusions
#
payments.excluded=klarna_account,klarna
#
# Switches
#
settings.datasourceproxy.enabled=false
settings.customerio.enabled=false
#
# Loyalty Factors & min/max
#
loyalty.minimums=100,150,200,350,500,700,1000
loyalty.maximums=150,250,350,550,750,1000,1500
loyalty.levelfactor=0.01
loyalty.wagerfactor=0.025

videoads.enabled=false

#
# Collectibles
#
collectibles.enabled=true

#
#
cpopups.enabled=true
cpopups.archiveTTL=P30D


