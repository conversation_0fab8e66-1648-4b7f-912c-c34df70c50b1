package com.ously.gamble.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.persistence.model.user.UserInfo;
import com.ously.gamble.util.AppSettingHelper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AppSettingHelperTest {

    @Test
    void testUserInfoMapHandling() {
        var ui = new UserInfo();
        var om = new ObjectMapper();
        assertFalse(AppSettingHelper.isTutorialShopPurchased(ui, om));
        AppSettingHelper.markUserInfoShopItemPurchased(ui, om);
        assertTrue(AppSettingHelper.isTutorialShopPurchased(ui, om));

        ui.setUserAppSettings("{\"map\":{\"a\":\"1\"}}" );
        assertTrue(AppSettingHelper.isTutorialAchievementClaimed(ui));
        AppSettingHelper.markUserInfoShopItemPurchased(ui, om);

        assertTrue(AppSettingHelper.isTutorialShopPurchased(ui, om));
        assertTrue(AppSettingHelper.isTutorialAchievementClaimed(ui));

    }

}
