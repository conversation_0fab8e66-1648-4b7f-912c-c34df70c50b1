package com.ously.gamble.services.social;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.features.TokenService;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.payload.TxPrice;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.user.UserTransactionRepository;
import com.ously.gamble.social.api.SocialInternalTokenService;
import com.ously.gamble.social.service.tokens.TreasureChestHelper;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
class TreasureChestHelperTest extends TestContext {

    @Autowired
    AchievementService achievementService;

    @Autowired
    UserTransactionRepository txRepo;

    @Autowired
    SocialInternalTokenService tkIntService;

    @Autowired
    TokenService tkService;

    @Autowired
    WalletRepository wRepo;

    @Autowired
    TreasureChestHelper tchHelper;


    @Test
    @Transactional
    @Order(1)
    void testTreasureChestTokenHelper() throws Exception {
        long userId = 1L;
        UserToken tk = new UserToken();
        tk.setType(TxPriceType.TCS);

        // Small Chest
        Optional<AwardedBonus> bonusForChest = tchHelper.getBonusForChest(userId, tk);
        assertTrue(bonusForChest.isPresent());
        AwardedBonus ab = bonusForChest.get();
        List<TxPrice> txPrices = TxPrice.parsePriceDef(ab.getPriceDef());
        assertTrue(txPrices.size() >= 2);

        // Small Chest
        tk.setType(TxPriceType.TCM);
        Set<Integer> pcSet = new HashSet<>();
        for (int i = 0; i < 100; i++) {
            bonusForChest = tchHelper.getBonusForChest(userId, tk);
            assertTrue(bonusForChest.isPresent());
            ab = bonusForChest.get();
            System.out.println("Iteration " + i + " -> " + ab.getPriceDef());
            txPrices = TxPrice.parsePriceDef(ab.getPriceDef());
            assertTrue(txPrices.size() >= 2);
            pcSet.add(txPrices.size());
        }
        // LU should appear in every 10th iteration
        assertTrue(pcSet.contains(3));
    }


}