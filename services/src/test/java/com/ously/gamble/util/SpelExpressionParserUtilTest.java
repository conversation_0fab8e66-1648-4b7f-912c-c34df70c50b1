package com.ously.gamble.util;

import com.ously.gamble.caching.LCacheFactoryTestImpl;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class SpelExpressionParserUtilTest {

    @Test
    void weekdayMatchSpelFilterTest() {
        var spel = new SpelExpressionParserUtil(new LCacheFactoryTestImpl<>());

        var zdt = ZonedDateTime.parse("2022-07-06T12:00:00Z");
        // WEEKDAY expressions
        assertTrue(spel.matchSpelFilter(zdt, "WEDNESDAY"));
        assertTrue(spel.matchSpelFilter(zdt, "WEDNESDAY,SATURDAY"));
        assertFalse(spel.matchSpelFilter(zdt, "MONDAY,TUESDAY,XXXX"));
        assertFalse(spel.matchSpelFilter(zdt, "SATURDAY"));
        // Spel expressions
        assertFalse(spel.matchSpelFilter(zdt, "!{'MONDAY','THURSDAY'}.contains(dayOfWeek.name)"));
        assertTrue(spel.matchSpelFilter(zdt, "!!{'MONDAY','THURSDAY'}.contains(dayOfWeek.name)"));

        assertTrue(spel.matchSpelFilter(zdt, "!{'WEDNESDAY'}.contains(dayOfWeek.name) && {'JULY'}" +
                                             ".contains(month.name)"));
        assertFalse(spel.matchSpelFilter(zdt, "!{'WEDNESDAY'}.contains(dayOfWeek.name) && " +
                                              "{'AUGUST'}" +
                                              ".contains(month.name)"));

        assertTrue(spel.matchSpelFilter(zdt, "!{'WEDNESDAY'}.contains(dayOfWeek.name) && {'JULY'}.contains(month.name) && hour >= 11 && hour < 13"));

        assertFalse(spel.matchSpelFilter(zdt, "!{'WEDNESDAY'}.contains(dayOfWeek.name) && " +
                                              "{'JULY'}" +
                                              ".contains(month.name) && hour >= 14 && hour < 23"));


//        assertFalse(spel.matchSpelFilter(zdt, "!SATURDAY,WEDNESDAY"));

    }
}