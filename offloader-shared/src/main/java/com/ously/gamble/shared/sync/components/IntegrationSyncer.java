package com.ously.gamble.shared.sync.components;

import com.ously.gamble.api.gamemanager.GMIntegration;
import com.ously.gamble.persistence.dto.GameIntegrationDto;
import com.ously.gamble.persistence.model.game.GameIntegration;
import com.ously.gamble.persistence.repository.game.GameIntegrationRepository;
import com.ously.gamble.shared.sync.SyncComponent;
import com.ously.gamble.shared.sync.SyncContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class IntegrationSyncer implements SyncComponent<GMIntegration> {
    private static final Logger log = LoggerFactory.getLogger(IntegrationSyncer.class);

    private final GameIntegrationRepository gIntRepo;

    public IntegrationSyncer(GameIntegrationRepository gIntR) {
        this.gIntRepo = gIntR;
    }


    @Override
    @Transactional(timeout = 120)
    public boolean doSync(List<GMIntegration> integrationsRemote, SyncContext ctx, boolean simulate) {

        var integrationsLocal = gIntRepo.getAllIntegrations();

        log.info("Got {} integrations from gamemanager, have {} integrations locally", integrationsRemote.size(), integrationsLocal.size());

        return performSync(integrationsLocal, integrationsRemote, simulate);
    }

    private boolean performSync(List<GameIntegrationDto> integrationsLocal, List<GMIntegration> integrationsRemote, boolean simulate) {

        var localIntegrations =
                integrationsLocal.stream().collect(Collectors.toMap(GameIntegrationDto::id,
                        GMIntegration::new));
        var remoteIntegrations = integrationsRemote.stream().collect(Collectors.toMap(GMIntegration::getId, Function.identity()));

        // 1.) iterate local and apply changes
        Map<Integer, GMIntegration> changedIntegrations = new HashMap<>();
        Map<Integer, GMIntegration> missingIntegrations = new HashMap<>();


        for (var csp : localIntegrations.values()) {
            var gmp = remoteIntegrations.get(csp.getId());
            if (gmp == null) {
                missingIntegrations.put(csp.getId(), csp);
            } else {
                // Check for changes
                var changed = false;

                if (!Objects.equals(csp.getDescription(), gmp.getDescription())) {
                    csp.setDescription(gmp.getDescription());
                    changed = true;

                }

                if (!Objects.equals(csp.getName(), gmp.getName())) {
                    csp.setName(gmp.getName());
                    changed = true;
                }

                if (!Objects.equals(csp.getStatus(), gmp.getStatus())) {
                    csp.setStatus(gmp.getStatus());
                    changed = true;
                }

                if (!Objects.equals(csp.getInfos(), gmp.getInfos())) {
                    csp.setInfos(gmp.getInfos());
                    changed = true;
                }


                if (changed) {
                    changedIntegrations.put(csp.getId(), csp);
                }
                remoteIntegrations.remove(csp.getId());
            }
        }

        Map<Integer, GMIntegration> newIntegrations = new HashMap<>();
        for (var gmp : remoteIntegrations.values()) {
            var csp = new GMIntegration();
            csp.setId(gmp.getId());
            csp.setName(gmp.getName());
            csp.setDescription(gmp.getDescription());
            csp.setStatus(gmp.getStatus());
            csp.setInfos(gmp.getInfos());
            newIntegrations.put(gmp.getId(), csp);
        }

        // Checking uniqueness of vendorName,bridgeName && slotCatalogName

        List<GMIntegration> allCSPs = new ArrayList<>(changedIntegrations.values());
        allCSPs.addAll(missingIntegrations.values());
        allCSPs.addAll(newIntegrations.values());

        var scNames = allCSPs.stream().map(GMIntegration::getName).collect(Collectors.toSet());
        var numCSPs = allCSPs.size();
        var success = true;
        if (scNames.size() != numCSPs) {
            log.warn("Integration name duplicates. Exp. {}, got {}", numCSPs, scNames.size());
            success = false;
        }


        log.info("Got {} integrations locally no longer in gamemanager, potential removal candidates", missingIntegrations.size());
        log.info("Got {} integrations changed", changedIntegrations.size());
        log.info("Got {} new integrations, creation candidates", newIntegrations.size());

        if (!simulate) {
            if (success) {
                // 1. perform updates
                if (updateIntegrations(changedIntegrations)) {
                    // 2. add new vendors
                    updateIntegrations(newIntegrations);
                }
            }
        }


        return success;
    }

    private boolean updateIntegrations(Map<Integer, GMIntegration> integrations) {

        integrations.values().forEach(a -> {
            var optInt = gIntRepo.findById(a.getId());
            if (optInt.isPresent()) {
                var gi = optInt.get();
                gi.setDescription(a.getDescription());
                gi.setStatus(a.getStatus());
                gi.setName(a.getName());
                gi.setInfos(a.getInfos());
                gIntRepo.save(gi);
            } else {
                var gi = new GameIntegration();
                gi.setId(a.getId());
                gi.setDescription(a.getDescription());
                gi.setStatus(a.getStatus());
                gi.setName(a.getName());
                gi.setInfos(a.getInfos());
                gIntRepo.save(gi);
            }
        });
        return true;
    }


}
