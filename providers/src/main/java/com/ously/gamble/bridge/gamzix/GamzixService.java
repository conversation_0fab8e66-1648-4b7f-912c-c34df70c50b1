package com.ously.gamble.bridge.gamzix;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.gamzix.payload.GZStatus;
import com.ously.gamble.bridge.gamzix.payload.GZStatusRequest;
import com.ously.gamble.bridge.gamzix.payload.GZTransactionRequest;

public interface GamzixService extends BridgeHandler {

    int GZ_SUCCESS = 200;
    int GZ_INVALIDSIG = 401;
    int GZ_INSUFFICIENTFUNDS = 402;
    int GZ_UNEXPECTED = 500;

    GZStatus balance(String pid);

    GZStatus withdraw(GZTransactionRequest req);

    GZStatus deposit(GZTransactionRequest req);

    GZStatus cancel(GZTransactionRequest req);

    GZStatus opened(GZStatusRequest req);

    GZStatus closed(GZStatusRequest req);
}
