package com.ously.gamble.bridge.everymatrix.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EMResponse {
	@JsonProperty("ApiVersion")
	String apiVersion;
	@JsonProperty("Request")
	String request;

	@JsonProperty("ReturnCode")
	int returnCode;
	@JsonProperty("Message")
	String message;

	public EMResponse(EMRequest req, int code, String msg) {
		apiVersion = req.getApiVersion();
		request = req.getRequest();
		returnCode = code;
		message = msg;
	}

	public EMResponse(String reqName, int code, String msg) {
		apiVersion = "1.0";
		request = reqName;
		returnCode = code;
		message = msg;
	}

	public String getApiVersion() {
		return apiVersion;
	}

	public void setApiVersion(final String apiVersion) {
		this.apiVersion = apiVersion;
	}

	public String getRequest() {
		return request;
	}

	public void setRequest(final String request) {
		this.request = request;
	}

	public int getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(final int returnCode) {
		this.returnCode = returnCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(final String message) {
		this.message = message;
	}
}
