package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class JRPCMSWithdrawRequest extends MSJsonRpcRequest {
    MSWithdrawRequest params;

    public MSWithdrawRequest getParams() {
        return params;
    }

    public void setParams(MSWithdrawRequest params) {
        this.params = params;
    }
}
