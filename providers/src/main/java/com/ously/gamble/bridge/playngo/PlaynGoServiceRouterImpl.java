package com.ously.gamble.bridge.playngo;

import com.ously.gamble.api.bridge.RouterTransportService;
import com.ously.gamble.bridge.RouterBaseV2;
import com.ously.gamble.bridge.playngo.payload.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Indexed;

@Indexed
public class PlaynGoServiceRouterImpl extends RouterBaseV2 implements PlaynGoService {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    public PlaynGoServiceRouterImpl(RouterTransportService rtService) {
        super(rtService);
    }


    @Override
    public PNGAuthenticateResp authorize(PNGAuthenticateReq req) {
        var target = getTarget(req.getAuthToken());
        var response = callRemote(target, "PlaynGoService::authorize", req);
        return (PNGAuthenticateResp) response;
    }

    @Override
    public PNGReserveResp reserve(PNGReserveReq req) {
        var target = getTarget(req.getExternalGameSessionId(), req.getExternalId());
        var response = callRemote(target, "PlaynGoService::reserve", req);
        return (PNGReserveResp) response;
    }

    @Override
    public PNGReleaseResp release(PNGReleaseReq req) {
        var target = getTarget(req.getExternalGameSessionId(), req.getExternalId());
        var response = callRemote(target, "PlaynGoService::release", req);
        return (PNGReleaseResp) response;
    }

    @Override
    public PNGBalanceResp balance(PNGBalanceReq req) {
        var target = getTarget(req.getExternalGameSessionId(), req.getExternalId());
        var response = callRemote(target, "PlaynGoService::balance", req);
        return (PNGBalanceResp) response;
    }

    @Override
    public PNGCancelReserveResp cancelReserve(PNGCancelReserveReq req) {
        var target = getTarget(req.getExternalGameSessionId(), req.getExternalId());
        var response = callRemote(target, "PlaynGoService::cancelReserve", req);
        return (PNGCancelReserveResp) response;
    }
}
