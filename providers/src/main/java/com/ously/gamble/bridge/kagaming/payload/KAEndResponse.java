package com.ously.gamble.bridge.kagaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class KAEndResponse extends KAResponse {
    public KAEndResponse() {
        super();
    }

    public KAEndResponse(int code, String status) {
        super(code, status);
    }


    @Override
    public String toString() {
        return "KAEndResponse{" +
                "status='" + status + '\'' +
                ", statusCode=" + statusCode +
                ", userMessage='" + userMessage + '\'' +
                '}';
    }
}
