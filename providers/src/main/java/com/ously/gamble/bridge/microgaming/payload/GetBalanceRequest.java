package com.ously.gamble.bridge.microgaming.payload;


import jakarta.xml.bind.annotation.*;

@XmlRootElement(name = "methodcall")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "methodcall", propOrder = {"auth", "call"})
public class GetBalanceRequest {

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "call")
    public static class GetBalanceCall {
        @XmlAttribute(name = "seq", required = true)
        private String seq;
        @XmlAttribute(name = "token", required = true)
        private String token;
        @XmlAttribute()
        private String clienttypeid;

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }


    @XmlAttribute(name = "name")
    private String name;
    @XmlAttribute(name = "timestamp")
    private String timestamp;
    @XmlAttribute(name = "system")
    private String system;
    @XmlElement()
    private Auth auth;
    @XmlElement(required = true)
    private GetBalanceCall call;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public Auth getAuth() {
        return auth;
    }

    public void setAuth(Auth auth) {
        this.auth = auth;
    }

    public GetBalanceCall getCall() {
        return call;
    }

    public void setCall(GetBalanceCall call) {
        this.call = call;
    }
}
