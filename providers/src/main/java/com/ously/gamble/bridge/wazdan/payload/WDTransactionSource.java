package com.ously.gamble.bridge.wazdan.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class WDTransactionSource {

    WDUser user;
    String roundId;
    String originalTransactionId;
    String transactionId;
    Integer gameId;

    public WDUser getUser() {
        return user;
    }

    public void setUser(WDUser user) {
        this.user = user;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(String originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Integer getGameId() {
        return gameId;
    }

    public void setGameId(Integer gameId) {
        this.gameId = gameId;
    }

    @Override
    public String toString() {
        return "WDTransactionSource{" +
                "user=" + user +
                ", roundId='" + roundId + '\'' +
                ", originalTransactionId='" + originalTransactionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", gameId=" + gameId +
                '}';
    }
}
