package com.ously.gamble.bridge.ladyluck;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.ladyluck.payload.*;

public interface LadyLuckService extends BridgeHandler {
    LLValidateTokenResponse validateToken(LLValidateTokenRequest req);

    LLGetBalanceResponse getBalance(LLGetBalanceRequest req);

    LLTransactionResponse debit(LLTransactionRequest req) throws OuslyTransactionException;

    LLTransactionResponse credit(LLTransactionRequest req) throws OuslyTransactionException;

    LLTransactionResponse rollback(LLTransactionRequest req);
}
