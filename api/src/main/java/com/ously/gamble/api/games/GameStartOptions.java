package com.ously.gamble.api.games;

import com.ously.gamble.api.leaderboards.GameLeaderboard;
import com.ously.gamble.api.leaderboards.LeaderboardType;
import com.ously.gamble.api.user.CasinoUserUnlockInfo;

import java.util.Map;

public record GameStartOptions(
        boolean unlocked,
        GameUnlockOption gameUnlockOption,
        Map<GameUnlockOption, Integer> unlockOptions,
        int unlockCost,
        int betLevelMax,
        int availableBetLevel,
        CasinoUserUnlockInfo unlocks,
        Map<LeaderboardType, GameLeaderboard> leaderboards,
        Map<Integer, GameRoomDefinition> rooms
) {
}
