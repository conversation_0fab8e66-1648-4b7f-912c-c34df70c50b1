package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

public class WebhookAddAchievementRequest extends WebhookRequest {
	@NotEmpty
	@JsonProperty(required = true, value = "userid")
	String userId;
	@NotEmpty String title;
	String message;
	@JsonProperty(value = "messageliteral")
	String messageLiteral;
	String rewards;
	String variables;
	String qualifier;

    public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getMessageLiteral() {
		return messageLiteral;
	}

	public void setMessageLiteral(String messageLiteral) {
		this.messageLiteral = messageLiteral;
	}

	public String getRewards() {
		return rewards;
	}

	public void setRewards(String rewards) {
		this.rewards = rewards;
	}

	public String getVariables() {
		return variables;
	}

	public void setVariables(String variables) {
		this.variables = variables;
	}

	public String getQualifier() {
		return qualifier;
	}

	public void setQualifier(String qualifier) {
		this.qualifier = qualifier;
	}

	@Override
	public String toString() {
		return "WebhookAddAchievementRequest{" + "userId='" + userId + '\'' + ", title='" + title + '\'' + ", message='" + message + '\'' + ", messageLiteral='" + messageLiteral + '\'' + ", rewards='" + rewards + '\'' + ", variables='" + variables + '\'' + ", qualifier='" + qualifier + '\'' + "} " + super.toString();
	}
}
