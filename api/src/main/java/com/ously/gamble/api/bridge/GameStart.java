package com.ously.gamble.api.bridge;

public record GameStart(Type type, String data, Long sessionId) {
    public enum Type {URL, HTML, NONE}

    public Type getType() {
        return type;
    }

    public String getData() {
        return data;
    }

    public boolean startViaURL() {
        return type == Type.URL;
    }

    public boolean startViaHTML() {
        return type == Type.HTML;
    }

}
