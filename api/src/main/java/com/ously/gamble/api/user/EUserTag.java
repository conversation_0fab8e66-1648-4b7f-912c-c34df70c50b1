package com.ously.gamble.api.user;

/**
 * Internally used Tags
 */
public enum EUserTag {

    TESTER(1, "Tester (internal&external)"),
    VIP(2, "VIP user"),
    ADMIN(3, "Administrative user"),
    PROVIDER(4, "Provider user"),
    WEB(5, "login via web"),
    IOS(6, "login via IOS"),
    ANDROID(7, "login via Android"),
    DEPOSITOR(8, "Purchaser/Depositor"),
    SUPPORTER(9, "Support team member"),


    AFFILIATE_PENDING(15, "Pro Affiliate Request"),
    AFFILIATE(16, "Pro Affiliate user"),
    LEGISLATOR(17, "Legislation user"),
    STREAMER(18, "Streamer"),
    STREAMER_BONUS(19, "Streamer, can use bonus like real money"),
    /**
     * User flags
     */
    MULTIACCOUNT(30, "User might be multiaccount"),
    MANUAL_PAYOUT(31, "All payouts must be manually approved"),
    KY<PERSON>_ON_PAYOUT(32, "User needs to do kyc upon next payout"),
    AML_RISK(33, "User seen as AML risk"),
    BLOCK_STRIKE(34, "User block candidate"),
    SELF_EXCLUDE(35, "User self excluded once"),

    /**
     * Rewards
     */

    NO_REWARDS(36, "User will not receive rewards"),

    /**
     * KYC Tags
     **/

    KYC_DONE(41, "User with KYC done. ID and address verified."),


    KYC_SOW_DONE(44, "User SOW verification done"),
    KYC_SOW_REQ(45, "User SOW verification needed"),

    KYC_ADDRESS_DONE(46, "User Address verification done"),
    KYC_ADDRESS_REQ(47, "User Address verification needed"),

    KYC_IDVER_DONE(48, "User ID verified"),
    KYC_IDVER_REQ(49, "User ID verification needed"),

    KYC_SDISC_DONE(50, "User self disclosure done"),
    KYC_SDISC_REQ(51, "User self disclosure needed"),

    PS_SHRIMP(60, "Not bought anything"),
    PS_OCTOPUS(61, "up to 5 EUR"),
    PS_FISH(62, "up to 10 EUR"),
    PS_SHARK(63, "up to 45 EUR"),
    PS_DOLPHIN(64, "up to 100"),
    PS_WHALE(65, "more than 100"),
    WT_BABY(66, "small whales, less than 1000"),
    WT_NORMAL(67, "normal 1000 - 5000"),
    WT_GIGANTIC(68, ">5000"),

    /**
     * VIP COMMUNITY
     */
    COMMUNITY_VIP(70, "Member of VIP Community"),

    /**
     * New VIP Segment Tags
     */
    //  from 300 to 499
    VIP_BRONZE(80, "300-499"),
    VIP_SILVER(81, "500-999"),
    VIP_GOLD(82, "1000-2999"),
    VIP_PLATINUM(83, "3000-9999999");


    final String description;
    final int id;

    EUserTag(int id, String desc) {
        this.id = id;
        this.description = desc;
    }

    public String description() {
        return description;
    }

    public int id() {
        return id;
    }
}
