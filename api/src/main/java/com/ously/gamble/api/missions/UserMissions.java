package com.ously.gamble.api.missions;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * per User entity which shows current (and upcoming) missions for platform
 * and the progress
 */
public class UserMissions implements Serializable {
    List<UserMission> missions;
    Map<Long, BigDecimal> progress;

    public UserMissions() {
        missions = Collections.emptyList();
        progress = Collections.emptyMap();
    }

    public UserMissions(List<UserMission> missions, Map<Long, BigDecimal> progress) {
        this.missions = missions;
        this.progress = progress;
    }

    public List<UserMission> getMissions() {
        return missions;
    }

    public void setMissions(List<UserMission> missions) {
        this.missions = missions;
    }

    public Map<Long, BigDecimal> getProgress() {
        return progress;
    }

    public void setProgress(Map<Long, BigDecimal> progress) {
        this.progress = progress;
    }
}
