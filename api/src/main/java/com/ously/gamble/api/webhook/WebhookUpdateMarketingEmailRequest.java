package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

public class WebhookUpdateMarketingEmailRequest extends WebhookRequest {
    @NotEmpty
    @JsonProperty(required = true, value = "userid")
    Long userId;
    String email;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "WebhookConfirmMarketingEmailRequest{" +
                "userId=" + userId +
                ", email='" + email + '\'' +
                '}';
    }
}
