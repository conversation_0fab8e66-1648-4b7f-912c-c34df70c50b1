package com.ously.gamble.payload.purchase;

import jakarta.validation.constraints.NotNull;

/**
 * { “purchaseID”: “string, “productID”: string, “receipt”: string }
 */
public class PurchaseVerificationResponse extends PurchaseVerificationRequest {
    @NotNull
    private Boolean verified = false;

    public PurchaseVerificationResponse(PurchaseVerificationRequest req, OuslyPurchaseReceipt opr) {
        super.setProductId(req.getProductId());
        super.setPurchaseId(req.getPurchaseId());
        super.setReceipt(req.getReceipt());
        if (opr != null) {
            this.verified = opr.getValidated();
        }
    }

    public PurchaseVerificationResponse(PurchaseVerificationRequest req) {
        this(req, null);
    }

    public PurchaseVerificationResponse() {
        super();
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(Boolean verified) {
        this.verified = verified;
    }
}
