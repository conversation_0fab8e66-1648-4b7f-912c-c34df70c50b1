<html>
<head>
    <title>${gameName}</title>
</head>
<body>
<div id="container"></div>
<script type="text/javascript"
        src="https://static.aleaplay.com/js/launch/bundle.js"></script>
<script type="text/javascript">

    const gameLauncher = new APGameLauncher({
        environmentCredential: "${envcreds}",
        signature: "${signature}",
        container: "#container",
        casinoPlayerId: "${playerId}",
        casinoSessionId: "${token}" ,
        gameId: "${gameId}",
        country: "${country}",
        currency: "${currency}",
        locale: "${locale}"
    });

    gameLauncher.play();
</script>

</body>
</html>

