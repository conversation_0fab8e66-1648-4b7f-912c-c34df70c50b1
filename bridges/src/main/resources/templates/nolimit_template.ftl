<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>

    <style>
        html, body {
            width: 100%;
            height: 100%;
        }

        body, #container {
            margin: 0;
            padding: 0;
            color: #000000;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -ms-flex-align: center;
            -webkit-align-items: center;
            align-items: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            width: 100%;
            height: 100%;

        }

        #game {
            width: 100%;
            height: 100%;
        }
    </style>

</head>
<body>
<div id="container"></div>

<script src="https://nolimitjs.nolimitcdn.com/dist/nolimit-latest.min.js"></script>

<script>
    console.log('nolimit.js', nolimit.version);

    var gameName = '${gameId}';
    var operator = '${operator}';
    var environment = '${environment}';
    var token = '${token}';
    var container = document.getElementById('container');
    var device = '${device}';
    var language = '${language}';

    nolimit.init({
        operator: operator,
        environment: environment,
        device: device
    });


    var gameElement = document.createElement('div');
    gameElement.id = 'game';

    container.appendChild(gameElement);

    var game = nolimit.load({
        target: gameElement,
        game: gameName,
        device: device,
        token: token,
        environment: environment,
        language: language,
        hideCurrency: true
    });

    game.on('exit', function () {
        var gameFrame = document.getElementById('game');
        gameFrame.parentNode.removeChild(gameFrame);
    });

    game.on('ready', function () {
        console.log(gameName, 'is loaded');
    });


</script>
</body>
</html>