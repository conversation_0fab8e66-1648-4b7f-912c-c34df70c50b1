package com.ously.gamble.bridge;

import com.ously.gamble.bridge.ballywulff.BWSigningTool;
import com.ously.gamble.bridge.ballywulff.BallyWulffConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

import static org.junit.jupiter.api.Assertions.assertTrue;

class BWSigningToolTest {

    BWSigningTool tool;

    private void setupTool() {
        var bwConfig = new BallyWulffConfiguration();
        tool = new BWSigningTool(bwConfig);
    }


    @Test
    public void testSignatureNoPayload() {
        setupTool();
        var headers = createGetSessionHeaders1();
        var canUri = "/bridge/ballywulff/game_sessions/action/get/727186d3-4761-4055-b657-aa55d232d660";
        var method = "POST";
        var payload = "[]";

        var b = tool.checkSignature(method, canUri, payload, headers);
        assertTrue(b);
    }

    @Test
    public void testSignatureGetSession() {
        setupTool();
        var headers = createGetSessionHeaders2();
        var canUri = "/bridge/ballywulff/game_sessions/action/get/8dc4c410-fb48-4dd9-87f7-3e223d8f4f6b";
        var method = "POST";
        var payload = "[]";

        var b = tool.checkSignature(method, canUri, payload, headers);
        assertTrue(b);
    }

    @Test
    public void testSignatureGetSessionNew() {
        setupTool();
        var headers = createGetSessionHeaders3();
        var canUri = "/bridge/ballywulff/game_sessions/action/wallet/236ca92f-2b77-4eec-907f-f62c95b6cd10";
        var method = "POST";
        var payload = "[]";

        var b = tool.checkSignature(method, canUri, payload, headers);
        assertTrue(b);
    }

    private MultiValueMap<String, String> createGetSessionHeaders1() {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("Authorization", "SHA256 Credential=KEYKEYKEYKEY, SignedHeaders=content-type;host;x-whow-date, Signature=e5c0eab025454084fd33b14ad4750f9450a87b568c8d019fc18d24bbab6bc0a7");
        headers.add("Accept", "application/json");
        headers.add("x-whow-date", "20200902T064804Z");
        headers.add("X-Forwarded-Proto", "https");
        headers.add("User-Agent", "Go-http-client/2.0");
        headers.add("X-Forwarded-For", "*************");
        headers.add("Host", "test.ouslygroup.com");
        headers.add("Accept-Encoding", "gzip");
        headers.add("Content-Length", "2");
        headers.add("X-Forwarded-Port", "443");
        headers.add("X-Amzn-Trace-Id", "Root=1-5f3f5cd8-c0d34a02a63dba6c7fc5b486");
        headers.add("Content-Type", "application/json; charset: utf-8");
        return headers;
    }

    private MultiValueMap<String, String> createGetSessionHeaders2() {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("Authorization", "SHA256 Credential=KEYKEYKEYKEY, SignedHeaders=content-type;host;x-whow-date, Signature=d9196c0f01ee17ba8fa724e52c66be9e5229369bd5a8f18ec7945c9fa75d9f8a");
        headers.add("Accept", "application/json");
        headers.add("x-whow-date", "20200902T072930Z");
        headers.add("X-Forwarded-Proto", "https");
        headers.add("User-Agent", "Go-http-client/2.0");
        headers.add("X-Forwarded-For", "*************");
        headers.add("Host", "test.ouslygroup.com");
        headers.add("Accept-Encoding", "gzip");
        headers.add("Content-Length", "2");
        headers.add("X-Forwarded-Port", "443");
        headers.add("X-Amzn-Trace-Id", "Root=1-5f3f5cd8-c0d34a02a63dba6c7fc5b486");
        headers.add("Content-Type", "application/json; charset: utf-8");
        return headers;
    }

    private MultiValueMap<String, String> createGetSessionHeaders3() {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add("Authorization", "SHA256 Credential=KEYKEYKEYKEY, SignedHeaders=content-type;host;x-whow-date, Signature=b4279a5df5ec1a21db7f9f43daa68b210a294041453a36b97bfe6fd8535bc72e");
        headers.add("Accept", "application/json");
        headers.add("X-Whow-Date", "20210127T054003Z");
        headers.add("X-Forwarded-Proto", "https");
        headers.add("User-Agent", "Go-http-client/2.0");
        headers.add("X-Forwarded-For", "*************");
        headers.add("Host", "test.ouslygroup.com:443");
        headers.add("Accept-Encoding", "gzip");
        headers.add("Content-Length", "2");
        headers.add("X-Forwarded-Port", "443");
        headers.add("X-Amzn-Trace-Id", "Root=1-5f3f5cd8-c0d34a02a63dba6c7fc5b486");
        headers.add("Content-Type", "application/json; charset: utf-8");
        return headers;
    }

}