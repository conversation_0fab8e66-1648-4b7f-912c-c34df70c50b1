package com.ously.gamble.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "slotcatalog")
public class SlotCatalogConfig {


    String baseUri = "https://api.slotcatalog.com/api/v1.0";
    String token = "Bqpg8zPA5S2exp5PJPxGHvETRs57zRwn";
    String clientId = "1005";
    String remoteApiKey = "BFUZ278rfnF381zbe43x33bd34!TZ";

    public String getBaseUri() {
        return baseUri;
    }

    public void setBaseUri(String baseUri) {
        this.baseUri = baseUri;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return clientId;
    }

    public String getRemoteApiKey() {
        return remoteApiKey;
    }

    public void setRemoteApiKey(String remoteApiKey) {
        this.remoteApiKey = remoteApiKey;
    }
}
