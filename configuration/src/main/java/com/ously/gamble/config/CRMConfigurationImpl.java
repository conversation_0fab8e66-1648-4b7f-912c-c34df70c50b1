package com.ously.gamble.config;


import com.ously.gamble.api.crm.CRMConfiguration;
import com.ously.gamble.api.crm.CRMDeviceConfiguration;
import com.ously.gamble.api.crm.CRMEventsConfiguration;
import com.ously.gamble.api.crm.CRMUserTagConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "crm")
public class CRMConfigurationImpl implements CRMConfiguration {

    boolean enabled;

    boolean updateRequests;


    CRMDeviceConfiguration devices = new CRMDeviceConfiguration();
    CRMUserTagConfiguration userTags = new CRMUserTagConfiguration();
    CRMEventsConfiguration events = new CRMEventsConfiguration();

    @Override
    public boolean isUpdateRequests() {
        return updateRequests;
    }

    public void setUpdateRequests(boolean updateRequests) {
        this.updateRequests = updateRequests;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void setDevices(CRMDeviceConfiguration devices) {
        this.devices = devices;
    }

    public void setUserTags(CRMUserTagConfiguration userTags) {
        this.userTags = userTags;
    }

    public void setEvents(CRMEventsConfiguration events) {
        this.events = events;
    }

    @Override
    public CRMEventsConfiguration getEvents() {
        return events;
    }

    @Override
    public CRMDeviceConfiguration getDevices() {
        return devices;
    }

    @Override
    public CRMUserTagConfiguration getUserTags() {
        return userTags;
    }


}
