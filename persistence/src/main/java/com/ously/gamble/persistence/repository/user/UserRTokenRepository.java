package com.ously.gamble.persistence.repository.user;

import com.ously.gamble.persistence.model.idclasses.UserRTokenId;
import com.ously.gamble.persistence.model.user.UserRToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRTokenRepository extends JpaRepository<UserRToken, UserRTokenId> {

    @Query("SELECT um FROM UserRToken um WHERE um.token = (:token)")
    Optional<UserRToken> getEntryForToken(@Param("token") String token);

    @Modifying
    @Query(nativeQuery = true,
            value = "delete from user_rtoken where expire_at < CURRENT_TIMESTAMP")
    void deleteExpiredRTokens();

    @Modifying
    @Query(nativeQuery = true, value = "delete from user_rtoken where user_id = :uid")
    void deleteExpiredRTokensForUser(@Param("uid") Long userId);

    List<UserRToken> findAllByUserId(long userId);
}
