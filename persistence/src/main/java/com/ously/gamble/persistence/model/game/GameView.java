package com.ously.gamble.persistence.model.game;

import jakarta.persistence.*;
import org.springframework.data.annotation.Immutable;

import java.time.Instant;
import java.time.LocalDate;

@Entity
@Immutable
@Table(name = "v_game_search")
public class GameView {
    @Id
    Long id;
    String gameId;
    String name;
    boolean active;
    boolean activeIos;
    boolean activeMobile;
    boolean activeDesktop;
    boolean activeAndroid;

    @Enumerated(EnumType.STRING)
    GameGenre genre;
    @Enumerated(EnumType.STRING)
    GameType type;
    @Enumerated(EnumType.STRING)
    GameJackpotMode jackpotMode;

    Long sortOrder;
    Instant updatedAt;
    Instant createdAt;
    @Column(name = "isLinked")
    boolean linked;
    Long infoId;
    String layout;
    Double officialRtp;

    Double gameRtp;

    Integer unlockLevel;

    Long slotrank;
    Long paylines;
    LocalDate releaseDate;
    String slotType;
    String volatility;
    Long spins;
    Double realRtp;
    Long sessions;
    Double sumBet;
    Double sumWin;
    Double avgSessionTime;
    boolean vendorActive;
    String blockedCountries;
    Integer vendorId;
    String vendorName;
    String vendorHomepage;
    int betLevels;
    String promoType;
    String thumbSourceDimension;
    boolean hasThumb;
    Long issuesLastMonth;
    Instant keyChangedAt;
    boolean valid;
    String tags;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isActiveIos() {
        return activeIos;
    }

    public void setActiveIos(boolean activeIos) {
        this.activeIos = activeIos;
    }

    public boolean isActiveMobile() {
        return activeMobile;
    }

    public void setActiveMobile(boolean activeMobile) {
        this.activeMobile = activeMobile;
    }

    public boolean isActiveDesktop() {
        return activeDesktop;
    }

    public void setActiveDesktop(boolean activeDesktop) {
        this.activeDesktop = activeDesktop;
    }

    public boolean isActiveAndroid() {
        return activeAndroid;
    }

    public void setActiveAndroid(boolean activeAndroid) {
        this.activeAndroid = activeAndroid;
    }

    public Long getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }

    public int getBetLevels() {
        return betLevels;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isLinked() {
        return linked;
    }

    public void setLinked(boolean linked) {
        this.linked = linked;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public Double getOfficialRtp() {
        return officialRtp;
    }

    public void setOfficialRtp(Double officialRtp) {
        this.officialRtp = officialRtp;
    }

    public Long getSlotrank() {
        return slotrank;
    }

    public void setSlotrank(Long slotrank) {
        this.slotrank = slotrank;
    }

    public Long getPaylines() {
        return paylines;
    }

    public void setPaylines(Long paylines) {
        this.paylines = paylines;
    }

    public LocalDate getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }

    public String getSlotType() {
        return slotType;
    }

    public void setSlotType(String slotType) {
        this.slotType = slotType;
    }

    public String getVolatility() {
        return volatility;
    }

    public void setVolatility(String volatility) {
        this.volatility = volatility;
    }

    public Long getSpins() {
        return spins;
    }

    public void setSpins(Long spins) {
        this.spins = spins;
    }

    public Double getRealRtp() {
        return realRtp;
    }

    public void setRealRtp(Double realRtp) {
        this.realRtp = realRtp;
    }

    public Long getSessions() {
        return sessions;
    }

    public void setSessions(Long sessions) {
        this.sessions = sessions;
    }

    public Double getSumBet() {
        return sumBet;
    }

    public void setSumBet(Double sumBet) {
        this.sumBet = sumBet;
    }

    public Double getSumWin() {
        return sumWin;
    }

    public void setSumWin(Double sumWin) {
        this.sumWin = sumWin;
    }

    public Double getAvgSessionTime() {
        return avgSessionTime;
    }

    public void setAvgSessionTime(Double avgSessionTime) {
        this.avgSessionTime = avgSessionTime;
    }

    public boolean isVendorActive() {
        return vendorActive;
    }

    public void setVendorActive(boolean vendorActive) {
        this.vendorActive = vendorActive;
    }

    public String getBlockedCountries() {
        return blockedCountries;
    }

    public void setBlockedCountries(String blockedCountries) {
        this.blockedCountries = blockedCountries;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorHomepage() {
        return vendorHomepage;
    }

    public void setVendorHomepage(String vendorHomepage) {
        this.vendorHomepage = vendorHomepage;
    }

    public String getPromoType() {
        return promoType;
    }

    public void setPromoType(String promoType) {
        this.promoType = promoType;
    }

    public String getThumbSourceDimension() {
        return thumbSourceDimension;
    }

    public void setThumbSourceDimension(String thumbSourceDimension) {
        this.thumbSourceDimension = thumbSourceDimension;
    }

    public boolean isHasThumb() {
        return hasThumb;
    }

    public void setHasThumb(boolean hasThumb) {
        this.hasThumb = hasThumb;
    }

    public Long getIssuesLastMonth() {
        return issuesLastMonth;
    }

    public void setIssuesLastMonth(Long issuesLastMonth) {
        this.issuesLastMonth = issuesLastMonth;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public Double getGameRtp() {
        return gameRtp;
    }

    public void setGameRtp(Double gameRtp) {
        this.gameRtp = gameRtp;
    }

    public Integer getUnlockLevel() {
        return unlockLevel;
    }

    public void setUnlockLevel(Integer unlockLevel) {
        this.unlockLevel = unlockLevel;
    }

    public void setBetLevels(int betLevels) {
        this.betLevels = betLevels;
    }

    public Instant getKeyChangedAt() {
        return keyChangedAt;
    }

    public void setKeyChangedAt(Instant keyChangedAt) {
        this.keyChangedAt = keyChangedAt;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }


    public GameGenre getGenre() {
        return genre;
    }

    public void setGenre(GameGenre genre) {
        this.genre = genre;
    }

    public GameType getType() {
        return type;
    }

    public void setType(GameType type) {
        this.type = type;
    }

    public GameJackpotMode getJackpotMode() {
        return jackpotMode;
    }

    public void setJackpotMode(GameJackpotMode jackpotMode) {
        this.jackpotMode = jackpotMode;
    }
}
