create or replace view v_user_search as
select u.id                                 AS id,
       u.status                             AS status,
       u.email                              AS email,
       u.local_id                           AS localId,
       u.created_at                         AS createdAt,
       COALESCE(u.system_user, false)       as `system`,
       u.affiliate_id                       AS linkedAffId,
       u.linked_date                        AS affLinkedDate,
       u.crm_update                         AS lastCrmUpdate,
       u.display_name                       AS displayName,
       w.balance                            AS balance,
       w.level                              AS level,
       w.saveup                             AS saveup,
       w.xp                                 AS xp,
       ui.name                              AS name,
       ui.surname                           AS surname,
       coalesce(ui.game_sound, FALSE)       AS gameSound,
       ui.birthdate                         AS birthdate,
       coalesce(ui.directContact, FALSE)    AS directContact,
       ui.gender                            AS gender,
       ui.mobile                            AS mobile,
       coalesce(ui.username_public, FALSE)  AS usernamePublic,
       coalesce(us.ss_session_count, 0)     AS sessionCount,
       coalesce(us.ss_sum_win, 0)           AS sumWin,
       coalesce(us.ss_sum_bet, 0)           AS sumBet,
       coalesce(us.ss_num_spins, 0)         AS sumSpins,
       ue.firstLogin                        AS firstLogin,
       ue.lastLogin                         AS lastLogin,
       ue.activeDays                        AS activeDays,
       coalesce(ue.iosLogins, 0)            AS iosLogins,
       coalesce(ue.webLogins, 0)            AS webLogins,
       coalesce(ue.androidLogins, 0)        AS androidLogins,
       ue.devices                           AS devices,
       coalesce(purch.count_purchases, 0)   AS countPurchases,
       coalesce(purch.sum_purchases, 0)     AS sumPurchases,
       coalesce(purch.first_purchase, 0)    AS firstPurchase,
       coalesce(purch.last_purchase, 0)     AS lastPurchase,
       concat(u.email, ',', u.display_name) AS usernameAndEmail
from users u
         left join user_statistics us
                   on u.id = us.user_id
         left join
     (select user_events.user_id                                      AS user_id,
             min(user_events.created_at)                              AS firstLogin,
             max(user_events.created_at)                              AS lastLogin,
             count(
                     distinct
                     date_format(user_events.created_at, '%Y-%m-%d')) AS activeDays,
             sum(if((user_events.platform = 'ANDROID'), 1, 0))        AS androidLogins,
             sum(if((user_events.platform = 'IOS'), 1, 0))            AS iosLogins,
             sum(if(
                     (user_events.platform not in ('IOS', 'ANDROID')),
                     1,
                     0))                                              AS webLogins,
             group_concat(
                     distinct
                     user_events.model
                     order
                     by
                     user_events.model
                     ASC
                     separator
                     ',')                                             AS devices
      from user_events
      where user_events.event_type = 'LOGIN'
      group by user_events.user_id) ue
     on u.id = ue.user_id
         left join
     (select purchases.user_id           AS user_id,
             count(0)                    AS count_purchases,
             sum(purchases.applied_cost) AS sum_purchases,
             min(purchases.created_at)   AS first_purchase,
             max(purchases.created_at)   AS last_purchase
      from purchases
      where purchases.status in ('SUCCESS', 'AUTHORIZED')
      group by purchases.user_id) purch on u.id = purch.user_id
         join wallets w on u.id = w.user_id
         left join user_info ui on u.id = ui.user_id;
