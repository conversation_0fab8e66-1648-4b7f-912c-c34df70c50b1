DROP TABLE ctransaction_round;

DROP TABLE transaction_round;

CREATE TABLE `session_round`
(
    `session_id` BIGINT       NOT NULL,
    `round_ref`  varchar(100) NOT NULL,
    `created_at` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `open`       bit(1)       NOT NULL DEFAULT b'1',
    `cancelled`  bit(1)       NOT NULL DEFAULT b'0',
    PRIMARY KEY (`session_id`, `round_ref`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    PARTITION BY KEY (`session_id`)
        PARTITIONS 10;


CREATE TABLE `csession_round`
(
    `round_ref`  varchar(100) NOT NULL,
    `session_id` BIGINT       NOT NULL,
    `user_id`    BIGINT       NOT NULL,
    `created_at` TIMESTAMP(6)          DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` TIMESTAMP(6)          DEFAULT CURRENT_TIMESTAMP(6),
    `open`       bit(1)       NOT NULL DEFAULT b'1',
    `cancelled`  bit(1)       NOT NULL DEFAULT b'0',
    PRIMARY KEY (`session_id`, `round_ref`, created_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    PARTITION BY RANGE (unix_timestamp(`created_at`))
        (PARTITION p_first VALUES LESS THAN (1609459200) ENGINE = InnoDB,
        PARTITION p202101 VALUES LESS THAN (1612137600) ENGINE = InnoDB,
        PARTITION p202102 VALUES LESS THAN (1614556800) ENGINE = InnoDB,
        PARTITION p202103 VALUES LESS THAN (1617235200) ENGINE = InnoDB,
        PARTITION p202104 VALUES LESS THAN (1619827200) ENGINE = InnoDB,
        PARTITION p202105 VALUES LESS THAN (1622505600) ENGINE = InnoDB,
        PARTITION p202106 VALUES LESS THAN (1625097600) ENGINE = InnoDB,
        PARTITION p202107 VALUES LESS THAN (1627776000) ENGINE = InnoDB,
        PARTITION p202108 VALUES LESS THAN (1630454400) ENGINE = InnoDB,
        PARTITION p202109 VALUES LESS THAN (1633046400) ENGINE = InnoDB,
        PARTITION p202110 VALUES LESS THAN (1635724800) ENGINE = InnoDB,
        PARTITION p202111 VALUES LESS THAN (UNIX_TIMESTAMP('2021-12-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-01-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-02-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-03-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-04-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p202112 VALUES LESS THAN (UNIX_TIMESTAMP('2022-05-01 00:00:00')) ENGINE = InnoDB,
        PARTITION p_future VALUES LESS THAN MAXVALUE ENGINE = InnoDB);
