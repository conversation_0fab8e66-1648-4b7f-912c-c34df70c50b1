CREATE  TRIGGER on_delete_sessstat_rd
    AFTER DELETE
    ON `session_statistics_rd`
    FOR EACH ROW
BEGIN
    delete from  session_statistics_ju where session_statistics_ju.rdate=OLD.rdate and session_statistics_ju.user_id=OLD.user_id and session_statistics_ju.session_id=OLD.session_id;
    delete from  session_statistics_jg where session_statistics_jg.rdate=OLD.rdate and session_statistics_jg.game_id=OLD.game_id and session_statistics_jg.session_id=OLD.session_id;
END
@@
CREATE TRIGGER on_insert_sessstat_rd
    AFTER INSERT
                     ON `session_statistics_rd`
                     FOR EACH ROW
BEGIN
insert into session_statistics_ju (rdate,user_id,session_id) values (NEW.rdate,NEW.user_id, NEW.session_id) ;
insert into session_statistics_jg (rdate,game_id,session_id) values ( NEW.rdate, NEW.game_id, NEW.session_id);
<PERSON><PERSON>
@@
CREATE  trigger on_insert_sessstat_lb
    after insert
    on session_statistics
    for each row
BEGIN
    insert into session_statistics_lb (rdate,session_id) values (  DATE(NEW.start_at), NEW.session_id);
<PERSON><PERSON>
@@
CREATE  trigger on_delete_sessstat_lb
    after delete
                     on session_statistics
                     for each row
BEGIN
delete from session_statistics_lb where rdate=DATE(OLD.start_at) and session_id=OLD.session_id;
END
@@


