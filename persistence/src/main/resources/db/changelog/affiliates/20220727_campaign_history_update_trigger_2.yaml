databaseChangeLog:
  - changeSet:
      id: af-20220727-01
      author: j<PERSON><PERSON>
      changes:
        - sqlFile:
            fullDefinition: false
            remarks: update trigger on update
            path: sql/campaign_trigger_on_update2.sql
            relativeToChangelogFile: true
            stripComments: true
            splitStatements: true
        - addColumn:
            tableName: aff_links
            columns:
              - column:
                  name: cpa
                  type: decimal(5)
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
              - column:
                  name: cpa_baseline
                  type: decimal(6,2)
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
              - column:
                  name: cpa_hit_at
                  type: TIMESTAMP
                  constraints:
                    nullable: true
        - sqlFile:
            fullDefinition: false
            remarks: update aff_links with cpa
            path: sql/update_aff_links_with_cpa.sql
            relativeToChangelogFile: true
            stripComments: true
            splitStatements: true
