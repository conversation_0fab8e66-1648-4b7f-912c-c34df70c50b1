package com.ously.gamble.persistence.model.json;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UnlockInfoTest {

    @Test
    void testUnlockGamesChange() {
        var ui = new UnlockInfo(null, null, null, null);
        var ui2 = ui.addUnlockedGame(100);
        assertArrayEquals(new int[]{100}, ui2.unlockedGames());
        ui2 = ui2.addUnlockedGame(101);
        assertArrayEquals(new int[]{100, 101}, ui2.unlockedGames());
        ui2 = ui2.addUnlockedGame(101);
        assertArrayEquals(new int[]{100, 101}, ui2.unlockedGames());
        ui2 = ui2.removeUnlockedGame(100);
        assertArrayEquals(new int[]{101}, ui2.unlockedGames());
        ui2 = ui2.removeUnlockedGame(101);
        assertNull(ui2.unlockedGames());
    }

    @Test
    void testUnlockVendorsChange() {
        var ui = new UnlockInfo(null, null, null, null);
        var ui2 = ui.addUnlockedVendor(100);
        assertArrayEquals(new int[]{100}, ui2.unlockedVendors());
        ui2 = ui2.addUnlockedVendor(101);
        assertArrayEquals(new int[]{100, 101}, ui2.unlockedVendors());
        ui2 = ui2.addUnlockedVendor(101);
        assertArrayEquals(new int[]{100, 101}, ui2.unlockedVendors());
        ui2 = ui2.removeUnlockedVendor(100);
        assertArrayEquals(new int[]{101}, ui2.unlockedVendors());
        ui2 = ui2.removeUnlockedVendor(101);
        assertNull(ui2.unlockedVendors());
    }

    @Test
    void testGameBetlevelChange() {
        var ui = new UnlockInfo(null, null, null, null);
        var ui2 = ui.setGameBetlevel(200, 2);
        assertArrayEquals(new String[]{"200=2"}, ui2.gameBetlevels());
        ui2 = ui2.setGameBetlevel(300, 1);
        assertArrayEquals(new String[]{"200=2", "300=1"}, ui2.gameBetlevels());
        ui2 = ui2.setGameBetlevel(200, 0);
        assertArrayEquals(new String[]{"300=1"}, ui2.gameBetlevels());
        ui2 = ui2.setGameBetlevel(300, 2);
        assertArrayEquals(new String[]{"300=2"}, ui2.gameBetlevels());
        ui2 = ui2.setGameBetlevel(300, 0);
        assertNull(ui2.gameBetlevels());
    }

    @Test
    void testVendorBetlevelChange() {
        var ui = new UnlockInfo(null, null, null, null);
        var ui2 = ui.setVendorBetlevel(200, 2);
        assertArrayEquals(new String[]{"200=2"}, ui2.vendorBetLevels());
        ui2 = ui2.setVendorBetlevel(300, 1);
        assertArrayEquals(new String[]{"200=2", "300=1"}, ui2.vendorBetLevels());
        ui2 = ui2.setVendorBetlevel(200, 0);
        assertArrayEquals(new String[]{"300=1"}, ui2.vendorBetLevels());
        ui2 = ui2.setVendorBetlevel(300, 2);
        assertArrayEquals(new String[]{"300=2"}, ui2.vendorBetLevels());
        ui2 = ui2.setVendorBetlevel(300, 0);
        assertNull(ui2.vendorBetLevels());
    }

    @Test
    void testEqualsAndHashcode() {
        var ui = new UnlockInfo(null, null, null, null);
        var ui2 = new UnlockInfo(null, null, null, null);

        assertEquals(ui, ui2);
        assertEquals(ui.hashCode(), ui2.hashCode());

        ui2 = new UnlockInfo(new int[]{1, 2, 3}, null, null, null);

        assertNotEquals(ui, ui2);
        assertNotEquals(ui.hashCode(), ui2.hashCode());


    }

}