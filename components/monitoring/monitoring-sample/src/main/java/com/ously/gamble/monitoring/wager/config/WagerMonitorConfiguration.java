package com.ously.gamble.monitoring.wager.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "monitor.wager")
public class WagerMonitorConfiguration {

    /**
     * enable the wager/user Edge monitor/incrementor
     */
    boolean enabled;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
