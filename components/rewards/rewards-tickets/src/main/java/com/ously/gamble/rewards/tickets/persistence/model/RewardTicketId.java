package com.ously.gamble.rewards.tickets.persistence.model;

import java.io.Serializable;

public class RewardTicketId implements Serializable {
    long userId;
    String qualifier;

    public RewardTicketId(long userId, String qualifier) {
        this.userId = userId;
        this.qualifier = qualifier;
    }

    public RewardTicketId() {
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getQualifier() {
        return qualifier;
    }

    public void setQualifier(String qualifier) {
        this.qualifier = qualifier;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RewardTicketId that = (RewardTicketId) o;

        if (userId != that.userId) return false;
        return qualifier.equals(that.qualifier);
    }

    @Override
    public int hashCode() {
        int result = Long.hashCode(userId);
        result = 31 * result + qualifier.hashCode();
        return result;
    }
}
