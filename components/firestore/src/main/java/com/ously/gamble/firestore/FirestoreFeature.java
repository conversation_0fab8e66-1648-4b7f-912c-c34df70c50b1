package com.ously.gamble.firestore;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import com.ously.gamble.conditions.ConditionalOnBackendOrOffloader;
import com.ously.gamble.firestore.configuration.FirestoreConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(prefix = "clouddb", name = "enabled", havingValue = "true")
@ConditionalOnBackendOrOffloader
public class FirestoreFeature extends AbstractPlatformFeature implements PlatformFeature {

    private final FirestoreConfig fConf;

    public FirestoreFeature(FirestoreConfig fConf) {
        this.fConf = fConf;
    }

    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Firestore channels " + ((fConf.isEnabled()) ? "enabled" : "disabled") + " env:" + fConf.getEnv(),
                "Firestore" +
                " " +
                "notification " +
                "channels");
    }
}
